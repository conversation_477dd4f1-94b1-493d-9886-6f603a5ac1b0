#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主界面布局模块
提供完整的主界面布局，包括菜单栏、工具栏、导航面板、工作区域等
"""

import tkinter as tk
from tkinter import ttk, messagebox


from ..core.base_gui import BaseGUI
from ..core.component_factory import get_component_factory
from ..core.event_manager import EventTypes
from ..modules.data_management.data_management_module import DataManagementModule
from ..modules.model_training.model_training_manager import ModelTrainingManager
from ..modules.visualization.visualization_manager import VisualizationManager
from ..components.status_indicator import StatusIndicator
from ..components.user_guide_widget import get_user_guide


class MainWindow(BaseGUI):
    """
    主窗口类
    提供完整的应用程序主界面
    """

    def __init__(self, parent: tk.Tk):
        """初始化主窗口"""
        self.current_tab = "data_management"
        super().__init__(parent)

        # 绑定事件
        self._bind_events()

    def _setup_ui(self):
        """设置主界面UI"""
        factory = get_component_factory()

        # 主框架
        if self.parent:
            self.main_frame = factory.create_frame(self.parent)
            self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建菜单栏
        self._create_menu_bar()

        # 创建工具栏
        self._create_toolbar()

        # 创建主要内容区域
        self._create_main_content()

        # 创建状态栏
        self._create_status_bar()

    def _create_menu_bar(self):
        """创建菜单栏"""
        if self.parent:
            menubar = tk.Menu(self.parent)
            # 类型检查：使用类型转换解决tk.Tk的config属性访问
            if hasattr(self.parent, 'config'):
                # 类型忽略：确保运行时parent是tk.Tk类型
                self.parent.config(menu=menubar)  # type: ignore

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="打开数据文件...", command=self._open_data_file)
        file_menu.add_command(label="保存项目", command=self._save_project)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self._on_exit)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="数据探索", command=lambda: self._switch_tab("data_management"))
        tools_menu.add_command(label="模型训练", command=lambda: self._switch_tab("model_training"))
        tools_menu.add_command(label="结果可视化", command=lambda: self._switch_tab("visualization"))
        tools_menu.add_command(label="集成学习", command=lambda: self._switch_tab("ensemble"))
        # 常用快捷入口（最小改动：切换标签页或触发已有方法）
        tools_menu.add_separator()
        tools_menu.add_command(label="开始训练", command=self._start_training_action)
        tools_menu.add_command(label="比较模型", command=self._compare_models_action)
        tools_menu.add_command(label="超参数调优", command=self._start_hyperparameter_tuning_action)
        tools_menu.add_command(label="DeLong检验", command=self._perform_delong_test_action)
        tools_menu.add_separator()
        tools_menu.add_command(label="生成报告", command=self._open_report_generator_action)

        # 会话菜单
        session_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="会话", menu=session_menu)
        session_menu.add_command(label="打开会话管理", command=self._open_session_manager_action)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用指南", command=self._show_help)
        help_menu.add_command(label="关于", command=self._show_about)

        self.register_component('menubar', menubar)

    def _create_toolbar(self):
        """创建工具栏"""
        if not self.main_frame:
            return

        factory = get_component_factory()
        toolbar = factory.create_frame(self.main_frame, relief=tk.RAISED, bd=1)
        toolbar.pack(fill=tk.X, padx=2, pady=2)

        # 快捷按钮
        buttons = [
            ("📁 打开数据", self._open_data_file, "打开数据文件"),
            ("💾 保存项目", self._save_project, "保存当前项目"),
            ("🚀 开始训练", self._start_training_action, "开始训练所选模型"),
            ("🔍 比较模型", self._compare_models_action, "比较已训练模型"),
            ("🔧 超参调优", self._start_hyperparameter_tuning_action, "启动超参数调优"),
            ("🧪 DeLong检验", self._perform_delong_test_action, "进行DeLong显著性检验"),
            ("📄 生成报告", self._open_report_generator_action, "打开报告生成器"),
            ("🔄 刷新", self._refresh_data, "刷新数据"),
            ("⚙️ 设置", self._show_settings, "打开设置"),
        ]

        for text, command, tooltip in buttons:
            btn = factory.create_button(toolbar, text=text, command=command, style='default')
            btn.pack(side=tk.LEFT, padx=2, pady=2)

            # 添加工具提示
            from ..core.utils import GUIUtils
            GUIUtils.create_tooltip(btn, tooltip)

        # 分隔符
        separator = ttk.Separator(toolbar, orient=tk.VERTICAL)
        separator.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)

        # 会话管理快捷
        session_btn = factory.create_button(toolbar, text="💾 会话管理", command=self._open_session_manager_action, style='default')
        session_btn.pack(side=tk.LEFT, padx=2, pady=2)
        from ..core.utils import GUIUtils
        GUIUtils.create_tooltip(session_btn, "打开会话管理器")

        # 分隔符
        separator2 = ttk.Separator(toolbar, orient=tk.VERTICAL)
        separator2.pack(side=tk.LEFT, fill=tk.Y, padx=5, pady=2)

        # 状态指示器
        from ..components.progress_widgets import StatusIndicator
        self.status_indicator = StatusIndicator(toolbar)

        self.register_component('toolbar', toolbar)

    def _create_main_content(self):
        """创建主要内容区域"""
        if not self.main_frame:
            return

        factory = get_component_factory()

        # 主内容区域
        content_frame = factory.create_frame(self.main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建左右分割面板
        paned_window = ttk.PanedWindow(content_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # 左侧导航面板
        self._create_navigation_panel(paned_window)

        # 右侧工作区域
        self._create_work_area(paned_window)

        self.register_component('content_frame', content_frame)
        self.register_component('paned_window', paned_window)

    def _create_navigation_panel(self, parent):
        """创建左侧导航面板"""
        if not parent:
            return

        factory = get_component_factory()

        # 导航框架
        nav_frame = factory.create_frame(parent, style='card')
        nav_frame.pack(fill=tk.BOTH, expand=True)

        # 导航标题
        nav_title = factory.create_label(nav_frame, text="功能导航", style='title')
        nav_title.pack(pady=10)

        # 导航树
        self.nav_tree = factory.create_treeview(nav_frame, show='tree')
        self.nav_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 添加导航项
        nav_items = [
            ("data_management", "📊 数据管理", [
                ("data_loader", "📁 数据加载"),
                ("data_preview", "👁️ 数据预览"),
                ("data_validation", "✅ 数据验证"),
                ("data_preprocessing", "⚙️ 数据预处理")
            ]),
            ("model_training", "🤖 模型训练", [
                ("model_selection", "🎯 模型选择"),
                ("training_config", "⚙️ 训练配置"),
                ("hyperparameter_tuning", "🔧 超参数调优"),
                ("training_monitor", "📈 训练监控")
            ]),
            ("visualization", "📈 结果可视化", [
                ("chart_manager", "📊 图表管理"),
                ("model_comparison", "🔍 模型比较"),
                ("shap_analysis", "🧠 SHAP分析"),
                ("report_generator", "📄 报告生成")
            ]),
            ("ensemble", "🔗 集成学习", [
                ("model_selector", "🎯 模型选择"),
                ("ensemble_config", "⚙️ 集成配置"),
                ("performance_evaluation", "📊 性能评估")
            ]),
            ("session", "💾 会话管理", [
                ("session_create", "➕ 创建会话"),
                ("session_load", "📂 加载会话"),
                ("session_save", "💾 保存会话")
            ])
        ]

        for parent_id, parent_text, children in nav_items:
            parent_node = self.nav_tree.insert('', 'end', iid=parent_id, text=parent_text, open=True)
            for child_id, child_text in children:
                self.nav_tree.insert(parent_node, 'end', iid=child_id, text=child_text)

        # 绑定选择事件
        self.nav_tree.bind('<<TreeviewSelect>>', self._on_nav_select)

        # 将导航面板添加到分割窗口
        parent.add(nav_frame, weight=1)

        self.register_component('nav_frame', nav_frame)
        self.register_component('nav_tree', self.nav_tree)

    def _create_work_area(self, parent):
        """创建右侧工作区域"""
        factory = get_component_factory()

        # 工作区框架
        work_frame = factory.create_frame(parent)
        work_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标签页容器
        self.notebook = factory.create_notebook(work_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建各个功能模块标签页
        self._create_module_tabs()

        # 将工作区添加到分割窗口
        parent.add(work_frame, weight=3)

        self.register_component('work_frame', work_frame)
        self.register_component('notebook', self.notebook)

    def _create_module_tabs(self):
        """创建功能模块标签页"""
        # 数据管理模块
        self.data_management_module = DataManagementModule(self.notebook)
        if self.data_management_module.main_frame:
            self.notebook.add(self.data_management_module.main_frame, text="数据管理")

        # 模型训练管理模块
        self.model_training_manager = ModelTrainingManager(self.notebook)
        if self.model_training_manager.main_frame:
            self.notebook.add(self.model_training_manager.main_frame, text="模型训练")

        # 可视化管理模块
        self.visualization_manager = VisualizationManager(self.notebook)
        if self.visualization_manager.main_frame:
            self.notebook.add(self.visualization_manager.main_frame, text="结果可视化")

        # 集成学习管理模块
        try:
            from ..modules.ensemble.ensemble_manager import EnsembleManager
            self.ensemble_manager = EnsembleManager(self.notebook)
            if self.ensemble_manager.main_frame:
                self.notebook.add(self.ensemble_manager.main_frame, text="集成学习")
        except ImportError as e:
            self.logger.warning(f"集成学习模块导入失败: {e}")
            # 创建占位符
            placeholder_frame = factory.create_frame(self.notebook)
            placeholder_label = factory.create_label(
                placeholder_frame,
                text="集成学习功能正在开发中...",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.notebook.add(placeholder_frame, text="集成学习")

        # 会话管理模块
        try:
            from ..modules.session_management.session_manager_gui import SessionManagerGUI
            self.session_manager_gui = SessionManagerGUI(self.notebook)
            if hasattr(self.session_manager_gui, 'main_frame') and self.session_manager_gui.main_frame:
                self.notebook.add(self.session_manager_gui.main_frame, text="会话管理")
        except ImportError as e:
            self.logger.warning(f"会话管理模块导入失败: {e}")
            # 创建占位符
            factory = get_component_factory()
            placeholder_frame = factory.create_frame(self.notebook)
            placeholder_label = factory.create_label(
                placeholder_frame,
                text="会话管理功能正在开发中...",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.notebook.add(placeholder_frame, text="会话管理")

        # 报告生成模块
        try:
            from ..modules.reporting.report_generator_gui import ReportGeneratorGUI
            self.report_generator_gui = ReportGeneratorGUI(self.notebook)
            if hasattr(self.report_generator_gui, 'main_frame') and self.report_generator_gui.main_frame:
                self.notebook.add(self.report_generator_gui.main_frame, text="报告生成")
        except ImportError as e:
            self.logger.warning(f"报告生成模块导入失败: {e}")
            # 创建占位符
            factory = get_component_factory()
            placeholder_frame = factory.create_frame(self.notebook)
            placeholder_label = factory.create_label(
                placeholder_frame,
                text="报告生成功能正在开发中...",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.notebook.add(placeholder_frame, text="报告生成")

        # 超参数调优模块（新增）
        try:
            from ..modules.hyperparameter_tuning.tuning_manager import HyperparameterTuningManager
            self.hyperparameter_tuning_manager = HyperparameterTuningManager(self.notebook)
            if hasattr(self.hyperparameter_tuning_manager, 'main_frame') and self.hyperparameter_tuning_manager.main_frame:
                self.notebook.add(self.hyperparameter_tuning_manager.main_frame, text="超参数调优")
        except ImportError as e:
            self.logger.warning(f"超参数调优模块导入失败: {e}")
            # 创建占位符
            factory = get_component_factory()
            placeholder_frame = factory.create_frame(self.notebook)
            placeholder_label = factory.create_label(
                placeholder_frame,
                text="超参数调优功能正在开发中...",
                style='title'
            )
            placeholder_label.pack(expand=True)
            self.notebook.add(placeholder_frame, text="超参数调优")

        # 其他模块的占位符标签页
        self._create_placeholder_tabs()

    def _create_placeholder_tabs(self):
        """创建其他模块的占位符标签页"""
        # 不再创建重复的集成学习和会话管理占位符选项卡
        # 这些功能已经在 _create_module_tabs() 中处理
        pass

    def _create_status_bar(self):
        """创建状态栏"""
        if not self.main_frame:
            return

        factory = get_component_factory()
        status_frame = factory.create_frame(self.main_frame, relief=tk.SUNKEN, bd=1)
        if status_frame:
            status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态指示器
        self.status_indicator = StatusIndicator(status_frame)
        if self.status_indicator.main_frame:
            self.status_indicator.main_frame.pack(side=tk.LEFT, padx=5, pady=2)

        # 状态文本
        self.status_var = tk.StringVar(value="就绪")
        status_label = factory.create_label(status_frame, textvariable=self.status_var, style='small')
        status_label.pack(side=tk.LEFT, padx=10, pady=2)

        # 进度条
        from ..components.progress_widgets import ProgressWidget
        self.progress_widget = ProgressWidget(status_frame, show_percentage=False, show_status=False)
        if self.progress_widget.main_frame:
            self.progress_widget.main_frame.pack(side=tk.RIGHT, padx=5, pady=2)

        # 时间显示
        self.time_var = tk.StringVar()
        time_label = factory.create_label(status_frame, textvariable=self.time_var, style='small')
        time_label.pack(side=tk.RIGHT, padx=5, pady=2)

        # 更新时间
        self._update_time()

        self.register_component('status_frame', status_frame)
        self.register_variable('status', self.status_var)
        self.register_variable('time', self.time_var)

    def _bind_events(self):
        """绑定事件"""
        # 订阅数据相关事件
        self.subscribe_event(EventTypes.DATA_LOADED, self._on_data_loaded)
        self.subscribe_event(EventTypes.DATA_PREPROCESSED, self._on_data_preprocessed)

        # 订阅模型相关事件
        self.subscribe_event(EventTypes.MODEL_TRAINING_STARTED, self._on_training_started)
        self.subscribe_event(EventTypes.MODEL_TRAINING_COMPLETED, self._on_training_completed)
        self.subscribe_event(EventTypes.MODEL_TRAINED, self._on_model_trained)

        # 订阅可视化相关事件
        self.subscribe_event(EventTypes.VISUALIZATION_REQUESTED, self._on_visualization_requested)

        # 订阅配置更新事件
        self.subscribe_event(EventTypes.CONFIG_UPDATED, self._on_config_updated)

        # 绑定标签页切换事件
        if hasattr(self, 'notebook') and self.notebook:
            self.notebook.bind('<<NotebookTabChanged>>', self._on_tab_changed)

    def _on_nav_select(self, event):
        """导航树选择事件处理"""
        selection = self.nav_tree.selection()
        if selection:
            item_id = selection[0]
            self._handle_navigation(item_id)

    def _handle_navigation(self, item_id: str):
        """处理导航选择"""
        # 根据选择的项目切换到对应的功能
        if item_id.startswith("data_"):
            self.notebook.select(0)  # 数据管理标签页
        elif item_id.startswith("model_"):
            self.notebook.select(1)  # 模型训练标签页
        elif item_id.startswith("chart_") or item_id.startswith("shap_"):
            self.notebook.select(2)  # 可视化标签页
        elif item_id.startswith("ensemble_"):
            self.notebook.select(3)  # 集成学习标签页
        elif item_id.startswith("session_"):
            self.notebook.select(4)  # 会话管理标签页

        self.status_var.set(f"已切换到: {self.nav_tree.item(item_id, 'text')}")

    def _switch_tab(self, tab_name: str):
        """切换标签页"""
        tab_mapping = {
            "data_management": 0,
            "model_training": 1,
            "visualization": 2,
            "ensemble": 3,
            "session": 4,
            "tuning": 5
        }

        if tab_name in tab_mapping:
            self.notebook.select(tab_mapping[tab_name])
            self.current_tab = tab_name

    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)

        # 每秒更新一次
        if self.parent:
            self.parent.after(1000, self._update_time)

    # 菜单和工具栏事件处理
    def _open_data_file(self):
        """打开数据文件"""
        from ..core.utils import GUIUtils

        file_path = GUIUtils.browse_file(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )

        if file_path:
            # 切换到数据管理标签页
            self._switch_tab("data_management")

            # 设置文件路径到数据管理模块
            self.data_management_module.file_selector.set_file_path(file_path)

            self.status_var.set(f"已选择文件: {file_path}")

    def _save_project(self):
        """保存项目"""
        self.show_info("保存项目", "项目保存功能正在开发中...")

    def _refresh_data(self):
        """刷新数据"""
        self.status_var.set("正在刷新数据...")
        # 这里可以添加刷新逻辑
        self.status_var.set("数据刷新完成")

    def _show_settings(self):
        """显示设置"""
        self.show_info("设置", "设置功能正在开发中...")

    def _show_help(self):
        """显示帮助"""
        help_text = """
重构版多模型集成机器学习平台使用指南

1. 数据管理：
   - 使用"数据加载"功能导入CSV文件
   - 在"数据预览"中查看数据概况
   - 通过"数据验证"检查数据质量
   - 使用"数据预处理"清洗和准备数据

2. 模型训练：
   - 选择合适的机器学习模型
   - 配置训练参数
   - 启动训练过程

3. 结果可视化：
   - 查看模型性能图表
   - 进行模型比较分析
   - 生成分析报告

4. 集成学习：
   - 选择最优模型组合
   - 配置集成策略
   - 评估集成效果

5. 会话管理：
   - 保存和恢复训练会话
   - 管理历史实验记录
        """

        # 创建帮助窗口
        help_window = tk.Toplevel(self.parent)
        help_window.title("使用指南")
        help_window.geometry("700x500")

        # 添加指导按钮
        button_frame = ttk.Frame(help_window)
        button_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(button_frame, text="📊 数据管理指导",
                  command=lambda: self._show_module_guide("data_management")).pack(side='left', padx=5)
        ttk.Button(button_frame, text="🤖 模型训练指导",
                  command=lambda: self._show_module_guide("model_training")).pack(side='left', padx=5)
        ttk.Button(button_frame, text="📈 可视化指导",
                  command=lambda: self._show_module_guide("visualization")).pack(side='left', padx=5)

        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def _show_module_guide(self, module_name: str):
        """显示模块指导"""
        user_guide = get_user_guide(self.parent)
        if user_guide:
            user_guide.show_module_guide(module_name)
        else:
            self.show_info("提示", f"用户指导功能暂时不可用")

    def _show_about(self):
        """显示关于信息"""
        about_text = """
重构版多模型集成机器学习平台 v2.0

基于模块化架构设计的新版本，具有以下特点：
• 模块化设计，易于维护和扩展
• 事件驱动架构，模块间解耦
• 统一的配置管理系统
• 可复用的GUI组件库
• 完整的日志记录系统

技术栈：
• Python 3.7+
• tkinter GUI框架
• pandas 数据处理
• scikit-learn 机器学习
• matplotlib/seaborn 可视化

开发团队：AI助手 Cascade
版本：2.0 (重构版)
更新日期：2025-08-07
        """

        messagebox.showinfo("关于", about_text)

    def _on_exit(self):
        """退出应用程序"""
        if self.ask_yes_no("确认退出", "确定要退出应用程序吗?"):
            if self.parent:
                self.parent.quit()

    # 事件处理方法
    def _on_data_loaded(self, data):
        """数据加载完成事件处理"""
        self.status_var.set("数据加载完成")
        self.status_indicator.set_status("success", "数据已加载")

    def _on_data_preprocessed(self, data):
        """数据预处理完成事件处理"""
        self.status_var.set("数据预处理完成")
        self.status_indicator.set_status("success", "数据预处理完成")

    def _on_config_updated(self, data):
        """配置更新事件处理"""
        self.status_var.set("配置已更新")

    def get_current_module(self):
        """获取当前活动的模块"""
        current_tab_index = self.notebook.index(self.notebook.select())

        if current_tab_index == 0:
            return self.data_management_module
        elif current_tab_index == 1:
            return self.model_training_manager
        elif current_tab_index == 2:
            return self.visualization_manager
        # 其他模块待实现
        return None

    def _on_training_started(self, data):
        """模型训练开始事件处理"""
        self.status_var.set("模型训练已开始")
        self.status_indicator.set_status("running", "正在训练模型...")
        if self.progress_widget:
            self.progress_widget.set_progress(0, "开始训练...")

    def _on_training_completed(self, data):
        """模型训练完成事件处理"""
        self.status_var.set("模型训练完成")
        self.status_indicator.set_status("success", "训练完成")
        if self.progress_widget:
            self.progress_widget.complete("训练完成")

    def _on_model_trained(self, data):
        """单个模型训练完成事件处理"""
        if data and 'model_name' in data:
            model_name = data['model_name']
            self.status_var.set(f"模型 {model_name} 训练完成")


    # ===== 常用入口动作（最小改动：切换标签页 + 委派到模块） =====
    def _start_training_action(self):
        """开始训练：切到“模型训练”页并触发开始训练按钮（若可用）"""
        try:
            self._switch_tab("model_training")
            if hasattr(self, 'model_training_manager') and getattr(self.model_training_manager, 'train_button', None):
                # 如果按钮存在且可用，调用其命令
                cmd = self.model_training_manager.train_button.cget('command')
                if callable(cmd):
                    cmd()
                else:
                    # 直接调用内部方法
                    if hasattr(self.model_training_manager, '_start_training'):
                        self.model_training_manager._start_training()
            else:
                self.show_info("提示", "请先在‘模型训练’页选择模型和配置，再点击开始训练。")
        except Exception as e:
            self.show_warning("提示", f"无法直接开始训练：{e}\n已切换到‘模型训练’页。")

    def _compare_models_action(self):
        """比较模型：切到‘可视化’页的‘模型比较’标签（占位为主）"""
        try:
            self._switch_tab("visualization")
            # 可选：若可视化模块提供生成比较图表的方法，可在此调用
            if hasattr(self, 'visualization_manager') and hasattr(self.visualization_manager, '_create_model_comparison_tab'):
                pass
            self.status_var.set("已切换到‘结果可视化’ - 模型比较")
        except Exception as e:
            self.show_warning("提示", f"无法执行模型比较：{e}\n已切换到‘结果可视化’页。")

    def _start_hyperparameter_tuning_action(self):
        """超参数调优：切到‘超参数调优’页"""
        self._switch_tab("tuning")
        self.status_var.set("已切换到‘超参数调优’页")

    def _perform_delong_test_action(self):
        """DeLong 检验：打开独立窗口并运行内置 GUI"""
        try:
            import tkinter as tk
            from ..modules.analysis.delong_test_gui import DeLongTestGUI

            top = tk.Toplevel(self.parent)
            top.title("DeLong 检验")
            top.geometry("900x650")

            # 让子窗口相对主窗口居中
            try:
                x = self.parent.winfo_x() + 60
                y = self.parent.winfo_y() + 60
                top.geometry(f"+{x}+{y}")
            except Exception:
                pass

            # 创建并持有引用，避免被回收
            self._delong_gui = DeLongTestGUI(top)
            self.status_var.set("已打开‘DeLong 检验’窗口")
        except Exception as e:
            self._switch_tab("visualization")
            self.show_warning("提示", f"DeLong 检验窗口打开失败：{e}\n已切换到‘结果可视化’页。")

    def _open_report_generator_action(self):
        """打开报告生成器：切到‘报告生成’标签页（如存在），否则提示"""
        # 报告生成器位于可视化管理器内单独模块或独立标签页
        try:
            # 若 MainWindow 已添加“报告生成”Notebook页，则直接切换
            if hasattr(self, 'notebook'):
                # 寻找文本为“报告生成”的页索引
                for i in range(self.notebook.index('end')):
                    if self.notebook.tab(i, 'text') == "报告生成":
                        self.notebook.select(i)
                        self.status_var.set("已打开‘报告生成’页")
                        return
            # 若未找到，切到可视化页并提示
            self._switch_tab("visualization")
            self.show_info("提示", "未找到单独‘报告生成’页，请在相关模块中生成或使用会话管理导出报告。")
        except Exception as e:
            self.show_warning("提示", f"打开报告生成器失败：{e}")

    def _open_session_manager_action(self):
        """打开会话管理：切到‘会话管理’页（如存在），否则提示"""
        try:
            # 直接切换到‘会话管理’标签页（在 _create_module_tabs 中已有添加）
            self._switch_tab("session")
            self.status_var.set("已打开‘会话管理’页")
        except Exception as e:
            self.show_warning("提示", f"无法打开会话管理：{e}")

    def _on_visualization_requested(self, data):
        """可视化请求事件处理"""
        self.status_var.set("正在生成可视化...")
        # 切换到可视化标签页
        self._switch_tab("visualization")

    def _on_tab_changed(self, event):
        """标签页切换事件处理"""
        if hasattr(self, 'notebook') and self.notebook:
            current_tab = self.notebook.index(self.notebook.select())
            tab_names = ["数据管理", "模型训练", "结果可视化", "集成学习", "会话管理"]
            if current_tab < len(tab_names):
                self.status_var.set(f"当前页面: {tab_names[current_tab]}")
                # 发布标签页切换事件
                self.publish_event(EventTypes.TAB_CHANGED, {
                    'tab_index': current_tab,
                    'tab_name': tab_names[current_tab]
                })

    def _update_time(self):
        """更新时间显示"""
        import datetime
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_var.set(current_time)

        # 每秒更新一次
        if self.parent:
            self.parent.after(1000, self._update_time)
